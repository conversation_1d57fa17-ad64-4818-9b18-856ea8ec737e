#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天氣預報查詢程式使用範例
"""

from weather_forecast import WeatherForecast

def example_usage():
    """使用範例"""
    
    # 請替換為您的API授權碼
    API_KEY = "YOUR_API_KEY_HERE"
    
    # 建立天氣查詢物件
    weather = WeatherForecast(API_KEY)
    
    print("=== 範例1: 查詢台北市天氣 ===")
    # 查詢台北市天氣
    data = weather.get_weather_forecast("臺北市")
    if data:
        weather_info = weather.parse_weather_data(data, "臺北市")
        weather.display_weather(weather_info)
    
    print("\n=== 範例2: 查詢全台灣天氣（僅顯示前3個縣市） ===")
    # 查詢全台灣天氣
    data = weather.get_weather_forecast()
    if data:
        weather_info = weather.parse_weather_data(data)
        # 只顯示前3個縣市的資料
        weather.display_weather(weather_info[:3])
    
    print("\n=== 範例3: 取得支援的縣市清單 ===")
    cities = weather.get_city_list()
    print("支援的縣市:")
    for i, city in enumerate(cities[:10], 1):  # 只顯示前10個
        print(f"{i:2d}. {city}")
    print("... 等等")

if __name__ == "__main__":
    print("請先在程式碼中設定您的API授權碼")
    print("然後執行此範例程式")
    
    # 取消註解下面這行來執行範例
    # example_usage()
