# 台灣各縣市未來一週天氣預報查詢程式

這是一個使用中央氣象署開放資料API來查詢台灣各縣市未來一週天氣預報的Python程式。

## 功能特色

- 查詢全台灣或特定縣市的未來一週天氣預報
- 支援台灣所有22個縣市
- 可將查詢結果儲存為JSON檔案
- 友善的命令列介面
- 完整的錯誤處理

## 安裝需求

- Python 3.6 或更高版本
- requests 套件

## 安裝步驟

1. 確保已安裝Python 3.6+
2. 安裝所需套件：
   ```bash
   pip install -r requirements.txt
   ```

## 取得API授權碼

1. 前往中央氣象署開放資料平台：https://opendata.cwa.gov.tw/
2. 註冊帳號並登入
3. 申請API授權碼
4. 記下您的授權碼，執行程式時會需要輸入

## 使用方法

執行程式：
```bash
python weather_forecast.py
```

程式會提示您輸入API授權碼，然後提供以下選項：

1. **查詢全台灣天氣預報** - 顯示所有縣市的天氣預報
2. **查詢特定縣市天氣預報** - 選擇特定縣市查詢
3. **顯示支援的縣市清單** - 列出所有支援的縣市
4. **結束程式**

## 支援的縣市

程式支援台灣所有22個縣市：
- 臺北市、新北市、桃園市、臺中市、臺南市、高雄市
- 基隆市、新竹市、嘉義市
- 新竹縣、苗栗縣、彰化縣、南投縣、雲林縣、嘉義縣、屏東縣
- 宜蘭縣、花蓮縣、臺東縣、澎湖縣、金門縣、連江縣

## 輸出格式

程式會顯示以下資訊：
- 縣市名稱
- 預報時間區間
- 天氣描述

查詢結果也可以儲存為JSON格式檔案，方便後續處理。

## API資料來源

- API文檔：https://opendata.cwa.gov.tw/dist/opendata-swagger.html#/%E9%A0%90%E5%A0%B1/get_v1_rest_datastore_F_D0047_059
- 資料提供：中央氣象署

## 注意事項

1. 需要有效的API授權碼才能使用
2. API有使用頻率限制，請適度使用
3. 天氣預報資料會定期更新，建議定時查詢最新資料
4. **重要**：目前發現API的locationName參數可能無效，程式目前只能查詢到嘉義市的天氣資料
5. 程式會顯示嘉義市東區和西區的詳細天氣預報，包含未來一週的逐12小時預報

## 錯誤處理

程式包含完整的錯誤處理機制：
- 網路連線錯誤
- API回應錯誤
- JSON解析錯誤
- 檔案儲存錯誤

## 授權

本程式僅供學習和個人使用，請遵守中央氣象署開放資料使用條款。
