#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台灣各縣市未來一週天氣預報查詢程式
使用中央氣象署開放資料API
API文檔: https://opendata.cwa.gov.tw/dist/opendata-swagger.html#/%E9%A0%90%E5%A0%B1/get_v1_rest_datastore_F_D0047_059
"""

import requests
import json
from datetime import datetime
from typing import Dict, List, Optional

class WeatherForecast:
    def __init__(self, api_key: str):
        """
        初始化天氣預報查詢器
        
        Args:
            api_key (str): 中央氣象署API授權碼
        """
        self.api_key = api_key
        self.base_url = "https://opendata.cwa.gov.tw/api/v1/rest/datastore/F-D0047-059"
        
        # 台灣縣市代碼對照表
        self.city_codes = {
            "臺北市": "63",
            "新北市": "65", 
            "桃園市": "68",
            "臺中市": "66",
            "臺南市": "67",
            "高雄市": "64",
            "基隆市": "10017",
            "新竹市": "10018",
            "嘉義市": "10020",
            "新竹縣": "10004",
            "苗栗縣": "10005",
            "彰化縣": "10007",
            "南投縣": "10008",
            "雲林縣": "10009",
            "嘉義縣": "10010",
            "屏東縣": "10013",
            "宜蘭縣": "10002",
            "花蓮縣": "10015",
            "臺東縣": "10014",
            "澎湖縣": "10016",
            "金門縣": "09020",
            "連江縣": "09007"
        }
    
    def get_city_list(self) -> List[str]:
        """取得支援的縣市清單"""
        return list(self.city_codes.keys())
    
    def get_weather_forecast(self, city_name: str = None) -> Dict:
        """
        查詢天氣預報

        Args:
            city_name (str, optional): 縣市名稱，如果不指定則查詢全台灣

        Returns:
            Dict: API回應資料
        """
        params = {
            "Authorization": self.api_key,
            "format": "JSON"
        }

        # 注意：目前這個API似乎只回傳嘉義市的資料，locationName參數可能無效
        # 如果指定縣市，加入locationName參數（但可能無效）
        if city_name and city_name in self.city_codes:
            params["locationName"] = city_name

        try:
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            return data
        except requests.exceptions.RequestException as e:
            print(f"API請求錯誤: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析錯誤: {e}")
            return None
    
    def parse_weather_data(self, data: Dict, city_name: str = None) -> List[Dict]:
        """
        解析天氣預報資料

        Args:
            data (Dict): API回應資料
            city_name (str, optional): 指定縣市名稱

        Returns:
            List[Dict]: 解析後的天氣資料
        """
        if not data or "records" not in data:
            print("錯誤: API回應中沒有找到records欄位")
            return []

        if "Locations" not in data["records"]:
            print("錯誤: API回應中沒有找到Locations欄位")
            return []

        weather_info = []
        locations_data = data["records"]["Locations"]

        for location_group in locations_data:
            locations_name = location_group.get("LocationsName", "")
            locations = location_group.get("Location", [])

            for location in locations:
                location_name = location.get("LocationName", "")

                # 如果指定縣市，檢查是否匹配（這裡是區域名稱，不是縣市名稱）
                # 我們需要根據LocationsName來判斷縣市
                if city_name and city_name != locations_name:
                    continue

                weather_elements = location.get("WeatherElement", [])

                # 找到天氣預報綜合描述
                weather_description_element = None
                for element in weather_elements:
                    if element.get("ElementName") == "天氣預報綜合描述":
                        weather_description_element = element
                        break

                if not weather_description_element:
                    print(f"在 {location_name} 中沒有找到天氣預報綜合描述")
                    print(f"可用的ElementName: {[e.get('ElementName') for e in weather_elements[:5]]}")
                    continue

                city_weather = {
                    "city": locations_name,
                    "district": location_name,
                    "forecasts": []
                }

                time_data = weather_description_element.get("Time", [])
                for time_period in time_data:
                    start_time = time_period.get("StartTime", "")
                    end_time = time_period.get("EndTime", "")
                    element_values = time_period.get("ElementValue", [])

                    if element_values:
                        weather_desc = element_values[0].get("WeatherDescription", "")

                        forecast = {
                            "start_time": start_time,
                            "end_time": end_time,
                            "weather_description": weather_desc
                        }

                        city_weather["forecasts"].append(forecast)

                weather_info.append(city_weather)


    
    def display_weather(self, weather_data: List[Dict]):
        """
        顯示天氣預報資訊

        Args:
            weather_data (List[Dict]): 解析後的天氣資料
        """
        if not weather_data:
            print("沒有找到天氣資料")
            return

        for city_data in weather_data:
            print(f"\n{'='*60}")
            print(f"縣市: {city_data['city']} - {city_data['district']}")
            print(f"{'='*60}")

            for i, forecast in enumerate(city_data['forecasts'], 1):
                try:
                    start_time = datetime.fromisoformat(forecast['start_time'].replace('Z', '+00:00'))
                    end_time = datetime.fromisoformat(forecast['end_time'].replace('Z', '+00:00'))

                    print(f"\n第{i}期預報:")
                    print(f"時間: {start_time.strftime('%m/%d %H:%M')} ~ {end_time.strftime('%m/%d %H:%M')}")
                    print(f"天氣: {forecast['weather_description']}")
                except Exception as e:
                    print(f"時間解析錯誤: {e}")
                    print(f"原始時間: {forecast['start_time']} ~ {forecast['end_time']}")
                    print(f"天氣: {forecast['weather_description']}")

    def save_to_file(self, weather_data: List[Dict], filename: str = "weather_forecast.json"):
        """
        將天氣資料儲存到檔案
        
        Args:
            weather_data (List[Dict]): 天氣資料
            filename (str): 檔案名稱
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(weather_data, f, ensure_ascii=False, indent=2)
            print(f"天氣資料已儲存到 {filename}")
        except Exception as e:
            print(f"儲存檔案錯誤: {e}")


def main():
    """主程式"""
    print("台灣各縣市未來一週天氣預報查詢程式")
    print("="*50)
    
    # 請在這裡輸入您的API授權碼
    api_key = "CWA-DE812B14-ABC1-4DD2-9840-5E35143EDD17"
    
    if not api_key:
        print("錯誤: 請提供有效的API授權碼")
        return
    
    weather = WeatherForecast(api_key)
    
    while True:
        print("\n選項:")
        print("1. 查詢全台灣天氣預報")
        print("2. 查詢特定縣市天氣預報")
        print("3. 顯示支援的縣市清單")
        print("4. 結束程式")
        
        choice = input("請選擇 (1-4): ").strip()
        
        if choice == "1":
            print("正在查詢全台灣天氣預報...")
            data = weather.get_weather_forecast()
            if data:
                weather_info = weather.parse_weather_data(data)
                weather.display_weather(weather_info)
                
                save_choice = input("\n是否要儲存到檔案? (y/n): ").strip().lower()
                if save_choice == 'y':
                    weather.save_to_file(weather_info)
        
        elif choice == "2":
            print("支援的縣市:")
            cities = weather.get_city_list()
            for i, city in enumerate(cities, 1):
                print(f"{i:2d}. {city}")
            
            city_name = input("\n請輸入縣市名稱: ").strip()
            if city_name in cities:
                print(f"正在查詢{city_name}天氣預報...")
                data = weather.get_weather_forecast(city_name)
                if data:
                    weather_info = weather.parse_weather_data(data, city_name)
                    weather.display_weather(weather_info)
                    
                    save_choice = input("\n是否要儲存到檔案? (y/n): ").strip().lower()
                    if save_choice == 'y':
                        filename = f"{city_name}_weather_forecast.json"
                        weather.save_to_file(weather_info, filename)
            else:
                print("錯誤: 不支援的縣市名稱")
        
        elif choice == "3":
            print("支援的縣市清單:")
            cities = weather.get_city_list()
            for i, city in enumerate(cities, 1):
                print(f"{i:2d}. {city}")
        
        elif choice == "4":
            print("程式結束")
            break
        
        else:
            print("錯誤: 請選擇有效的選項 (1-4)")


if __name__ == "__main__":
    main()
