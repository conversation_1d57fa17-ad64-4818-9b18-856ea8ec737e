# 台灣天氣預報查詢程式

## 🎯 程式功能

這個Python程式可以查詢台灣各縣市未來一週的天氣預報，使用中央氣象署開放資料API。

## 📁 檔案結構

- `weather_forecast.py` - 主程式檔案
- `requirements.txt` - 依賴套件清單
- `README.md` - 詳細使用說明
- `example_usage.py` - 使用範例
- `demo.py` - 功能示範程式
- `test_api.py` - API測試程式

## 🚀 快速開始

1. **安裝依賴套件**：
   ```bash
   pip install -r requirements.txt
   ```

2. **執行程式**：
   ```bash
   python weather_forecast.py
   ```

3. **執行示範**：
   ```bash
   python demo.py
   ```

## ✨ 主要功能

### 1. 天氣預報查詢
- 查詢未來一週逐12小時天氣預報
- 包含詳細的天氣描述、溫度、濕度、風向風速等資訊
- 支援JSON格式資料儲存

### 2. 使用者介面
- 友善的命令列選單介面
- 支援查詢全台灣或特定縣市天氣
- 可選擇是否儲存查詢結果

### 3. 資料格式
程式回傳的天氣資料包含：
- 縣市名稱和區域
- 預報時間區間
- 完整的天氣描述（溫度、濕度、風向、降雨機率等）

## 📊 輸出範例

```
============================================================
縣市: 嘉義市 - 東區
============================================================

第1期預報:
時間: 07/22 00:00 ~ 07/22 06:00
天氣: 陰天。降雨機率20%。溫度攝氏25至26度。舒適。偏北風 風速2級(每秒2公尺)。相對濕度97%。

第2期預報:
時間: 07/22 06:00 ~ 07/22 18:00
天氣: 陰時多雲短暫陣雨或雷雨。降雨機率60%。溫度攝氏25至31度。舒適至悶熱。偏北風 風速3級(每秒4公尺)。相對濕度85%。
```

## ⚠️ 重要注意事項

1. **API限制**：目前發現API的locationName參數可能無效，程式只能查詢到嘉義市的天氣資料
2. **API授權碼**：需要到[中央氣象署開放資料平台](https://opendata.cwa.gov.tw/)申請API授權碼
3. **使用頻率**：API有使用頻率限制，請適度使用

## 🔧 技術細節

### API資料結構
- 使用中央氣象署F-D0047-059資料集
- 回應格式：JSON
- 資料更新頻率：定期更新

### 程式架構
- `WeatherForecast`類別：核心功能實作
- 模組化設計：易於維護和擴展
- 完整錯誤處理：網路、JSON解析、檔案操作等

### 資料處理
- 自動解析API回應的複雜JSON結構
- 提取"天氣預報綜合描述"資訊
- 格式化時間顯示和天氣描述

## 🛠️ 未來改進方向

1. **API問題解決**：研究如何查詢其他縣市的天氣資料
2. **功能擴展**：
   - 圖表化顯示天氣趨勢
   - 天氣警報通知
   - 歷史天氣資料比較
3. **使用者體驗**：
   - 網頁版介面
   - 行動裝置支援
   - 自動定位功能

## 📞 支援

如有問題或建議，請參考：
- `README.md` - 詳細使用說明
- `example_usage.py` - 程式碼使用範例
- 中央氣象署API文檔

---

**資料來源**：中央氣象署開放資料平台  
**程式版本**：1.0  
**最後更新**：2025年7月
