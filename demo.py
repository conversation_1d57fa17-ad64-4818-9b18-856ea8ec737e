#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天氣預報程式示範
"""

from weather_forecast import WeatherForecast

def demo():
    """示範程式功能"""
    print("=== 台灣天氣預報程式示範 ===")
    
    # API授權碼
    api_key = "CWA-DE812B14-ABC1-4DD2-9840-5E35143EDD17"
    
    # 建立天氣查詢物件
    weather = WeatherForecast(api_key)
    
    print("\n1. 查詢天氣預報...")
    data = weather.get_weather_forecast()
    
    if data:
        print("✓ API查詢成功")
        weather_info = weather.parse_weather_data(data)
        
        if weather_info:
            print(f"✓ 成功解析 {len(weather_info)} 個區域的天氣資料")
            
            # 只顯示第一個區域的前3個預報
            first_area = weather_info[0]
            print(f"\n=== {first_area['city']} - {first_area['district']} 天氣預報 (前3期) ===")
            
            for i, forecast in enumerate(first_area['forecasts'][:3], 1):
                print(f"\n第{i}期預報:")
                print(f"時間: {forecast['start_time'][:16]} ~ {forecast['end_time'][:16]}")
                print(f"天氣: {forecast['weather_description'][:50]}...")
            
            print(f"\n✓ 完整預報包含 {len(first_area['forecasts'])} 個時間段")
            
            # 儲存到檔案
            weather.save_to_file(weather_info, "demo_weather.json")
            print("✓ 天氣資料已儲存到 demo_weather.json")
            
        else:
            print("✗ 無法解析天氣資料")
    else:
        print("✗ API查詢失敗")
    
    print("\n=== 示範完成 ===")

if __name__ == "__main__":
    demo()
