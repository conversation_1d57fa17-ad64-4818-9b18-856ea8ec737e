#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試API回應結構
"""

import requests
import json

def test_api():
    api_key = "CWA-DE812B14-ABC1-4DD2-9840-5E35143EDD17"
    base_url = "https://opendata.cwa.gov.tw/api/v1/rest/datastore/F-D0047-059"
    
    params = {
        "Authorization": api_key,
        "format": "JSON"
    }
    
    try:
        response = requests.get(base_url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        print("完整API回應結構:")
        print(json.dumps(data, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"錯誤: {e}")

if __name__ == "__main__":
    test_api()
